using DrawnUi.Draw;
using Sandbox.ViewModels;

namespace Sandbox.Views.Controls
{
    public class ShaderCell : SkiaLayout
    {
        private SkiaImage _image;
        private SkiaShaderEffect _shaderEffect;
        private SkiaLabel _label;

        public ShaderCell()
        {
            BuildUI();
        }

        private void BuildUI()
        {
            this.Fill()
                .WithCache(SkiaCacheType.Image);

            // Create the image with shader effect using improved fluent pattern
            _image = new SkiaImage()
                .WithAspect(TransformAspect.AspectCover)
                .Fill()
                .WithCache(SkiaCacheType.Image);

            // Create and attach shader effect
            _shaderEffect = new SkiaShaderEffect();
            _image.VisualEffects.Add(_shaderEffect);

            // Create shadow background for text readability
            var shadowBackground = new SkiaShape()
                .Shape(ShapeType.Rectangle)
                .WithBackgroundColor(Color.FromArgb("#80000000"))
                .FillX()
                .EndY()
                .Height(30);

            // Create the title label
            _label = new SkiaLabel()
                .WithFontSize(12)
                .WithTextColor(Colors.White)
                .CenterX()
                .EndY()
                .WithMargin(new Thickness(4))
                .WithHorizontalTextAlignment(DrawTextAlignment.Center)
                .WithCache(SkiaCacheType.Operations);

            // Add all elements to layout
            this.AddSubView(_image);
            this.AddSubView(shadowBackground);
            this.AddSubView(_label);
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext is ShaderItem item)
            {
                _image.Source = item.ImageSource;
                _shaderEffect.ShaderSource = item.ShaderFilename;
                _label.Text = item.Title;
            }
        }
    }
}

using DrawnUi.Draw;
using Sandbox.ViewModels;

namespace Sandbox.Views.Controls
{
    public class ShaderCell : SkiaLayout
    {
        public ShaderCell()
        {
            Type = LayoutType.Grid;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            UseCache = SkiaCacheType.Image;
            
            // Create the image with shader effect
            _image = new SkiaImage
            {
                Aspect= TransformAspect.AspectCover,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                UseCache = SkiaCacheType.Image
            };

            // Create the shader effect
            _shaderEffect = new SkiaShaderEffect();
            _image.VisualEffects.Add(_shaderEffect);

            // Create the label for the title
            _label = new SkiaLabel
            {
                FontSize = 12,
                TextColor = Colors.White,
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.End,
                Margin = new Thickness(4),
                HorizontalTextAlignment = DrawTextAlignment.Center,
                UseCache = SkiaCacheType.Operations
            };

            // Add shadow background for better text readability
            var shadowBackground = new SkiaShape
            {
                Type = ShapeType.Rectangle,
                BackgroundColor = Color.FromArgb("#80000000"),
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.End,
                HeightRequest = 30
            };

            AddSubView(_image);
            AddSubView(shadowBackground);
            AddSubView(_label);
        }

        private readonly SkiaImage _image;
        private readonly SkiaShaderEffect _shaderEffect;
        private readonly SkiaLabel _label;

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext is ShaderItem item)
            {
                _image.Source = item.ImageSource;
                _shaderEffect.ShaderSource = item.ShaderFilename;
                _label.Text = item.Title;
            }
        }
    }
}

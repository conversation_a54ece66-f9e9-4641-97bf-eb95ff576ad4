﻿uniform float4 iMouse;           
uniform float  iTime;            
uniform float2 iResolution;      
uniform float2 iImageResolution; 
uniform shader iImage1;  
uniform float2 iOffset;  
uniform float2 iOrigin; 

const float COLOR_LEVELS = 4.0;
const float GLOW_RADIUS = 3.0;
const float PULSE_SPEED = 2.0;

/// <summary>
/// Maps luminance to vibrant neon colors
/// </summary>
/// <param name="luminance">Input luminance value</param>
/// <param name="time">Current time for animation</param>
/// <returns>Neon color</returns>
half3 neonColorMap(float luminance, float time) {
    float pulseFactor = sin(time * PULSE_SPEED) * 0.2 + 1.0;
    
    if (luminance > 0.75) {
        return half3(1.0, 0.2, 0.8) * pulseFactor;
    } else if (luminance > 0.5) {
        return half3(0.2, 1.0, 0.8) * pulseFactor;
    } else if (luminance > 0.25) {
        return half3(0.8, 0.2, 1.0) * pulseFactor;
    } else {
        return half3(1.0, 0.8, 0.2) * pulseFactor;
    }
}

/// <summary>
/// Creates posterized color effect
/// </summary>
/// <param name="color">Input color</param>
/// <returns>Posterized color</returns>
half3 posterize(half3 color) {
    return floor(color * COLOR_LEVELS) / COLOR_LEVELS;
}

/// <summary>
/// Creates neon glow effect around edges
/// </summary>
/// <param name="coord">Current coordinate</param>
/// <returns>Glow intensity</returns>
float neonGlow(float2 coord) {
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (coord - iOffset) * renderingScale;
    float2 texelSize = 1.0 / iImageResolution.xy;
    
    float glow = 0.0;
    for (float x = -GLOW_RADIUS; x <= GLOW_RADIUS; x++) {
        for (float y = -GLOW_RADIUS; y <= GLOW_RADIUS; y++) {
            float2 offset = float2(x, y) * texelSize;
            half4 sample = iImage1.eval(inputCoord + offset);
            float sampleLum = dot(sample.rgb, half3(0.299, 0.587, 0.114));
            
            float distance = length(float2(x, y));
            float weight = exp(-distance * distance * 0.1);
            
            glow += sampleLum * weight;
        }
    }
    
    return glow * 0.02;
}

/// <summary>
/// Main fragment shader for neon pop art effect
/// </summary>
/// <param name="fragCoord">Current pixel coordinate</param>
/// <returns>Neon styled pixel color</returns>
half4 main(float2 fragCoord) {
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    
    half4 originalColor = iImage1.eval(inputCoord);
    
    half3 posterizedColor = posterize(originalColor.rgb);
    float luminance = dot(posterizedColor, half3(0.299, 0.587, 0.114));
    
    half3 neonColor = neonColorMap(luminance, iTime);
    
    float glowEffect = neonGlow(fragCoord);
    neonColor += glowEffect * half3(1.0, 0.5, 1.0);
    
    float contrast = 1.3;
    neonColor = (neonColor - 0.5) * contrast + 0.5;
    
    neonColor = clamp(neonColor, 0.0, 1.0);
    
    return half4(neonColor, originalColor.a);
}
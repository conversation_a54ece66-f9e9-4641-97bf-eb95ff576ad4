﻿uniform float4 iMouse;            
uniform float  iTime;             
uniform float2 iResolution;      
uniform float2 iImageResolution;  
uniform shader iImage1;  
uniform shader iImage2;   
uniform float2 iOffset;  
uniform float2 iOrigin;  
uniform float4 iMargins;

/// <summary>
/// Color dodge blend mode
/// </summary>
/// <param name="src">Source color</param>
/// <param name="dst">Destination color</param>
/// <returns>Blended color</returns>
half3 colorDodge(in half3 src, in half3 dst) {
    return step(0.0, dst) * mix(min(half3(1.0), dst / (1.0 - src)), half3(1.0), step(1.0, src)); 
}

/// <summary>
/// Converts color to grayscale
/// </summary>
/// <param name="col">Input color</param>
/// <returns>Grayscale value</returns>
float greyScale(in half3 col) {
    return dot(col, half3(0.3, 0.59, 0.11));
}

/// <summary>
/// Detects edges for continuous line drawing
/// </summary>
/// <param name="coord">Current coordinate</param>
/// <returns>Edge intensity</returns>
float detectEdges(float2 coord) {
    float2 texelSize = 1.0 / iImageResolution.xy;
    
    half4 center = iImage1.eval(coord);
    half4 left = iImage1.eval(coord - float2(texelSize.x, 0.0));
    half4 right = iImage1.eval(coord + float2(texelSize.x, 0.0));
    half4 up = iImage1.eval(coord - float2(0.0, texelSize.y));
    half4 down = iImage1.eval(coord + float2(0.0, texelSize.y));
    
    float centerLum = greyScale(center.rgb);
    float leftLum = greyScale(left.rgb);
    float rightLum = greyScale(right.rgb);
    float upLum = greyScale(up.rgb);
    float downLum = greyScale(down.rgb);
    
    float sobelX = -leftLum + rightLum;
    float sobelY = -upLum + downLum;
    
    return sqrt(sobelX * sobelX + sobelY * sobelY);
}

/// <summary>
/// Creates directional blur for line continuity
/// </summary>
/// <param name="coord">Current coordinate</param>
/// <param name="direction">Blur direction</param>
/// <returns>Blurred color</returns>
half3 directionalBlur(float2 coord, float2 direction) {
    half3 result = half3(0.0);
    float totalWeight = 0.0;
    
    for (int i = -3; i <= 3; i++) {
        float weight = exp(-float(i * i) * 0.2);
        float2 sampleCoord = coord + direction * float(i) * 2.0;
        result += iImage1.eval(sampleCoord).rgb * weight;
        totalWeight += weight;
    }
    
    return result / totalWeight;
}

/// <summary>
/// Main fragment shader for continuous sketch lines effect
/// </summary>
/// <param name="fragCoord">Current pixel coordinate</param>
/// <returns>Sketched pixel color with continuous lines</returns>
half4 main(float2 fragCoord) {
    float2 renderingScale = iImageResolution.xy / iResolution.xy;
    float2 inputCoord = (fragCoord - iOffset) * renderingScale;
    float2 q = inputCoord / iImageResolution.xy;
    
    half3 col = iImage1.eval(inputCoord).rgb;
    
    // Create smooth blur instead of random sampling
    half3 blurred = half3(0.0);
    float totalWeight = 0.0;
    
    // Use a smooth blur kernel
    for (int i = -2; i <= 2; i++) {
        for (int j = -2; j <= 2; j++) {
            float weight = exp(-float(i*i + j*j) * 0.2);
            float2 sampleCoord = inputCoord + float2(float(i), float(j)) * 1.5;
            blurred += iImage1.eval(sampleCoord).rgb * weight;
            totalWeight += weight;
        }
    }
    blurred = blurred / totalWeight;
    
    // Detect edges for line creation
    float edgeStrength = detectEdges(inputCoord);
    
    // Create directional blur based on edge direction
    float2 texelSize = 1.0 / iImageResolution.xy;
    float gradX = greyScale(iImage1.eval(inputCoord + float2(texelSize.x, 0.0)).rgb) - 
                  greyScale(iImage1.eval(inputCoord - float2(texelSize.x, 0.0)).rgb);
    float gradY = greyScale(iImage1.eval(inputCoord + float2(0.0, texelSize.y)).rgb) - 
                  greyScale(iImage1.eval(inputCoord - float2(0.0, texelSize.y)).rgb);
    
    float2 edgeDirection = normalize(float2(-gradY, gradX));
    half3 directionalBlurred = directionalBlur(inputCoord, edgeDirection);
    
    // Mix blurs based on edge strength
    blurred = mix(blurred, directionalBlurred, edgeStrength * 0.7);
    
    half3 inv = half3(1.0) - blurred; 
    half3 lighten = colorDodge(col, inv);
    half3 res = half3(greyScale(lighten));
    
    // Enhance contrast for cleaner lines
    res = half3(pow(res.x, 6.0)); 
    
    // Add subtle vignette effect
    res *= 0.3 + 0.7 * pow(16.0 * q.x * q.y * (1.0 - q.x) * (1.0 - q.y), 0.2);
    
    return half4(res, 1.0); 
}
global using DrawnUi.Draw;
global using SkiaSharp;
using Microsoft.Extensions.Logging;

namespace Sandbox.CameraShaders
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            Super.NavBarHeight = 47;

            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    fonts.AddFont("OpenSans-Semibold.ttf", "FontTextTitle");
                    fonts.AddFont("OpenSans-Regular.ttf", "FontText");
                });

            builder.UseDrawnUi(new()
            {
                UseDesktopKeyboard = true,
                DesktopWindow = new()
                {
                    Width = 500,
                    Height = 700,
                }
            });

            if (Super.SkiaGeneration == 2)
            {
                ShadersFolder = "Shaders2";
            }

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }

        public static string ShadersFolder = "Shaders";
    }
}
